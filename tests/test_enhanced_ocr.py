"""
Tests for Enhanced OCR functionality with LMStudio integration.
"""

import unittest
from unittest.mock import patch, MagicMock, AsyncMock
import pytest
from fastapi.testclient import TestClient
from io import BytesIO
from PIL import Image

from main import app
from services.lmstudio_service import LMStudioService, LMStudioConfig
from services.prompt_service import CambodianID<PERSON>romptService
from controllers.enhanced_ocr_controller import <PERSON>hancedOC<PERSON><PERSON>roller
from schemas.enhanced_ocr import (
    EnhancedCambodianIDCardResult,
    ProcessingRequest,
    ConfidenceLevel,
    ProcessingMethod
)

class TestLMStudioService(unittest.TestCase):
    """Test LMStudio service functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = LMStudioConfig(
            host="localhost",
            port=1234,
            model_name="test-model",
            timeout=10
        )
    
    def test_config_from_env(self):
        """Test configuration creation from environment variables."""
        with patch.dict('os.environ', {
            'LMSTUDIO_HOST': 'test-host',
            'LMSTUDIO_PORT': '5678',
            'LMSTUDIO_MODEL_NAME': 'test-env-model'
        }):
            config = LMStudioConfig.from_env()
            self.assertEqual(config.host, 'test-host')
            self.assertEqual(config.port, 5678)
            self.assertEqual(config.model_name, 'test-env-model')
    
    @patch('services.lmstudio_service.lms', None)
    def test_service_unavailable_when_lms_not_installed(self):
        """Test service reports unavailable when LMStudio SDK not installed."""
        service = LMStudioService(self.config)
        self.assertFalse(service.is_available)
    
    @patch('services.lmstudio_service.lms')
    def test_service_available_when_lms_installed(self, mock_lms):
        """Test service reports available when LMStudio SDK is installed."""
        service = LMStudioService(self.config)
        self.assertTrue(service.is_available)
    
    @patch('services.lmstudio_service.lms')
    async def test_enhance_ocr_text_success(self, mock_lms):
        """Test successful OCR text enhancement."""
        # Mock LMStudio components
        mock_model = MagicMock()
        mock_model.complete.return_value = "Enhanced text result"
        mock_lms.llm.return_value = mock_model
        
        service = LMStudioService(self.config)
        service._model = mock_model
        
        result = await service.enhance_ocr_text(
            "raw khmer text",
            "raw english text",
            "test prompt: {khmer_text} {english_text}"
        )
        
        self.assertTrue(result["success"])
        self.assertEqual(result["enhanced_text"], "Enhanced text result")
        self.assertEqual(result["model_used"], "test-model")
    
    async def test_enhance_ocr_text_unavailable(self):
        """Test OCR enhancement when service is unavailable."""
        service = LMStudioService(self.config)
        service._available = False
        
        result = await service.enhance_ocr_text(
            "raw khmer text",
            "raw english text",
            "test prompt"
        )
        
        self.assertFalse(result["success"])
        self.assertIn("error", result)


class TestPromptService(unittest.TestCase):
    """Test prompt service functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.prompt_service = CambodianIDPromptService()
    
    def test_initialization(self):
        """Test prompt service initialization."""
        self.assertIsNotNone(self.prompt_service.templates)
        self.assertIn("text_extraction", self.prompt_service.templates)
        self.assertIn("structured_extraction", self.prompt_service.templates)
    
    def test_get_template(self):
        """Test getting specific templates."""
        template = self.prompt_service.get_template("text_extraction")
        self.assertIsNotNone(template)
        self.assertEqual(template.name, "text_extraction")
        
        # Test non-existent template
        template = self.prompt_service.get_template("non_existent")
        self.assertIsNone(template)
    
    def test_get_structured_extraction_prompt(self):
        """Test getting structured extraction prompt."""
        prompt = self.prompt_service.get_structured_extraction_prompt()
        self.assertIsInstance(prompt, str)
        self.assertIn("JSON", prompt)
        self.assertIn("ឈ្មោះ", prompt)  # Khmer label for name
    
    def test_get_field_specific_prompt(self):
        """Test getting field-specific prompts."""
        prompt = self.prompt_service.get_field_specific_prompt("name")
        self.assertIsInstance(prompt, str)
        self.assertIn("name", prompt.lower())
        
        prompt = self.prompt_service.get_field_specific_prompt("id_number")
        self.assertIn("digits", prompt.lower())
    
    def test_list_available_templates(self):
        """Test listing available templates."""
        templates = self.prompt_service.list_available_templates()
        self.assertIsInstance(templates, dict)
        self.assertIn("text_extraction", templates)
        self.assertIn("structured_extraction", templates)


class TestEnhancedOCRController(unittest.TestCase):
    """Test enhanced OCR controller functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.controller = EnhancedOCRController()
    
    @patch('controllers.enhanced_ocr_controller.get_lmstudio_service')
    @patch('controllers.enhanced_ocr_controller.get_prompt_service')
    async def test_controller_initialization(self, mock_prompt_service, mock_lmstudio_service):
        """Test controller initialization."""
        mock_lmstudio = MagicMock()
        mock_lmstudio.initialize = AsyncMock(return_value=True)
        mock_lmstudio_service.return_value = mock_lmstudio
        
        success = await self.controller.initialize()
        self.assertTrue(success)
        self.assertTrue(self.controller._initialized)
    
    def test_calculate_overall_confidence(self):
        """Test overall confidence calculation."""
        result = EnhancedCambodianIDCardResult()
        
        # Set some high confidence fields
        result.full_name.value = "Test Name"
        result.full_name.confidence = ConfidenceLevel.HIGH
        result.id_number.value = "123456789"
        result.id_number.confidence = ConfidenceLevel.HIGH
        
        confidence = self.controller._calculate_overall_confidence(result)
        self.assertEqual(confidence, ConfidenceLevel.HIGH)
    
    def test_calculate_quality_score(self):
        """Test quality score calculation."""
        result = EnhancedCambodianIDCardResult()
        
        # Set some extracted fields
        result.full_name.value = "Test Name"
        result.full_name.confidence = ConfidenceLevel.HIGH
        result.id_number.value = "123456789"
        result.id_number.confidence = ConfidenceLevel.MEDIUM
        
        score = self.controller._calculate_quality_score(result)
        self.assertIsInstance(score, float)
        self.assertGreater(score, 0.0)
        self.assertLessEqual(score, 1.0)


class TestEnhancedOCRAPI(unittest.TestCase):
    """Test Enhanced OCR API endpoints."""
    
    def setUp(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def create_test_image(self) -> BytesIO:
        """Create a test image for upload."""
        image = Image.new('RGB', (100, 100), color='white')
        img_bytes = BytesIO()
        image.save(img_bytes, format='JPEG')
        img_bytes.seek(0)
        return img_bytes
    
    def test_health_check(self):
        """Test health check endpoint."""
        response = self.client.get("/ocr/health")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("status", data)
        self.assertIn("lmstudio_available", data)
    
    def test_get_available_prompts(self):
        """Test getting available prompts."""
        response = self.client.get("/ocr/prompts/available")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("available_templates", data)
        self.assertIn("total_count", data)
    
    def test_get_processing_stats(self):
        """Test getting processing statistics."""
        response = self.client.get("/ocr/stats")
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("system_status", data)
        self.assertIn("supported_features", data)
        self.assertIn("supported_languages", data)
    
    @patch('controllers.enhanced_ocr_controller.get_enhanced_ocr_controller')
    def test_enhanced_ocr_endpoint_invalid_file(self, mock_controller):
        """Test enhanced OCR endpoint with invalid file type."""
        # Test with non-image file
        response = self.client.post(
            "/ocr/idcard/enhanced",
            files={"file": ("test.txt", "text content", "text/plain")}
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn("Invalid file type", response.json()["detail"])
    
    @patch('controllers.enhanced_ocr_controller.process_cambodian_id_enhanced')
    def test_enhanced_ocr_endpoint_success(self, mock_process):
        """Test successful enhanced OCR processing."""
        # Mock successful processing
        mock_result = EnhancedCambodianIDCardResult()
        mock_result.full_name.value = "Test Name"
        mock_result.overall_confidence = ConfidenceLevel.HIGH
        mock_result.processing_metadata.processing_method = ProcessingMethod.LMSTUDIO_ENHANCED
        
        mock_process.return_value = mock_result
        
        img_bytes = self.create_test_image()
        response = self.client.post(
            "/ocr/idcard/enhanced",
            files={"file": ("test.jpg", img_bytes, "image/jpeg")}
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("full_name", data)
        self.assertIn("overall_confidence", data)


@pytest.mark.asyncio
class TestAsyncFunctionality:
    """Test async functionality with pytest."""
    
    async def test_lmstudio_service_async_methods(self):
        """Test async methods in LMStudio service."""
        config = LMStudioConfig(model_name="test-model")
        service = LMStudioService(config)
        
        # Test with unavailable service
        service._available = False
        result = await service.enhance_ocr_text("test", "test", "test")
        assert "error" in result
        assert not result["success"]
    
    async def test_enhanced_controller_async_processing(self):
        """Test async processing in enhanced controller."""
        controller = EnhancedOCRController()
        
        # Test initialization
        with patch.object(controller.lmstudio_service, 'initialize', return_value=True):
            success = await controller.initialize()
            assert success


if __name__ == '__main__':
    # Run unittest tests
    unittest.main(verbosity=2)

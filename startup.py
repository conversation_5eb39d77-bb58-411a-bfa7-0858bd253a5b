#!/usr/bin/env python3
"""
Startup script for Cambodian ID Card OCR API.

This script helps initialize and start the enhanced OCR system with proper checks.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.lmstudio_service import get_lmstudio_service, initialize_lmstudio_service
from controllers.enhanced_ocr_controller import get_enhanced_ocr_controller

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("Startup")

def check_system_dependencies():
    """Check if required system dependencies are available."""
    logger.info("Checking system dependencies...")
    
    issues = []
    
    # Check Tesseract
    try:
        import pytesseract
        tesseract_cmd = pytesseract.pytesseract.tesseract_cmd
        if not os.path.exists(tesseract_cmd):
            issues.append(f"Tesseract not found at {tesseract_cmd}")
        else:
            logger.info(f"✓ Tesseract found at {tesseract_cmd}")
    except ImportError:
        issues.append("pytesseract package not installed")
    
    # Check OpenCV
    try:
        import cv2
        logger.info(f"✓ OpenCV version {cv2.__version__}")
    except ImportError:
        issues.append("opencv-python package not installed")
    
    # Check PIL/Pillow
    try:
        from PIL import Image
        logger.info("✓ PIL/Pillow available")
    except ImportError:
        issues.append("Pillow package not installed")
    
    # Check LMStudio SDK
    try:
        import lmstudio
        logger.info("✓ LMStudio SDK available")
    except ImportError:
        logger.warning("⚠ LMStudio SDK not installed (enhanced features will be disabled)")
        logger.info("  Install with: pip install lmstudio")
    
    return issues

def check_environment_config():
    """Check environment configuration."""
    logger.info("Checking environment configuration...")
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        logger.warning("⚠ .env file not found")
        logger.info("  Copy .env.example to .env and configure as needed")
    else:
        logger.info("✓ .env file found")
    
    # Check critical environment variables
    critical_vars = [
        "DATABASE_URL",
    ]
    
    missing_vars = []
    for var in critical_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"⚠ Missing environment variables: {', '.join(missing_vars)}")
    else:
        logger.info("✓ Critical environment variables set")
    
    return missing_vars

async def check_lmstudio_connection():
    """Check LMStudio connection and model availability."""
    logger.info("Checking LMStudio connection...")
    
    try:
        service = get_lmstudio_service()
        
        if not service.is_available:
            logger.warning("⚠ LMStudio SDK not available")
            return False
        
        # Try to initialize
        success = await initialize_lmstudio_service()
        
        if success:
            logger.info("✓ LMStudio connection successful")
            
            # Check model
            model_name = service.config.model_name or "default"
            logger.info(f"✓ Using model: {model_name}")
            
            return True
        else:
            logger.warning("⚠ LMStudio connection failed")
            logger.info("  Make sure LMStudio is running on localhost:1234")
            logger.info("  And that a model is loaded")
            return False
            
    except Exception as e:
        logger.error(f"✗ LMStudio check failed: {e}")
        return False

async def initialize_services():
    """Initialize all services."""
    logger.info("Initializing services...")
    
    try:
        # Initialize enhanced OCR controller
        controller = get_enhanced_ocr_controller()
        success = await controller.initialize()
        
        if success:
            logger.info("✓ Enhanced OCR controller initialized")
        else:
            logger.warning("⚠ Enhanced OCR controller partially initialized")
        
        return success
        
    except Exception as e:
        logger.error(f"✗ Service initialization failed: {e}")
        return False

def print_startup_summary(
    dependency_issues,
    missing_env_vars,
    lmstudio_available,
    services_initialized
):
    """Print startup summary."""
    print("\n" + "="*60)
    print("CAMBODIAN ID CARD OCR API - STARTUP SUMMARY")
    print("="*60)
    
    # System status
    print("\n📋 SYSTEM STATUS:")
    if dependency_issues:
        print("  ✗ System dependencies: ISSUES FOUND")
        for issue in dependency_issues:
            print(f"    - {issue}")
    else:
        print("  ✓ System dependencies: OK")
    
    if missing_env_vars:
        print("  ⚠ Environment config: INCOMPLETE")
        for var in missing_env_vars:
            print(f"    - Missing: {var}")
    else:
        print("  ✓ Environment config: OK")
    
    # Service status
    print("\n🚀 SERVICES:")
    print("  ✓ Traditional OCR: AVAILABLE")
    
    if lmstudio_available:
        print("  ✓ Enhanced OCR (AI): AVAILABLE")
        print("  ✓ Text validation: AVAILABLE")
        print("  ✓ Error correction: AVAILABLE")
    else:
        print("  ⚠ Enhanced OCR (AI): UNAVAILABLE")
        print("  ⚠ Text validation: UNAVAILABLE")
        print("  ⚠ Error correction: UNAVAILABLE")
    
    # API endpoints
    print("\n🌐 API ENDPOINTS:")
    print("  Traditional OCR:")
    print("    POST /ocr/idcard")
    
    if lmstudio_available:
        print("  Enhanced OCR:")
        print("    POST /ocr/idcard/enhanced")
        print("    POST /ocr/idcard/enhanced/compatible")
        print("    POST /ocr/idcard/compare")
    
    print("  System:")
    print("    GET  /ocr/health")
    print("    GET  /ocr/stats")
    print("    GET  /docs (API documentation)")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    
    if dependency_issues:
        print("  1. Install missing system dependencies")
        print("     See README.md for installation instructions")
    
    if not lmstudio_available:
        print("  2. Install and configure LMStudio for enhanced features:")
        print("     - Download LMStudio from https://lmstudio.ai/")
        print("     - Install a model (recommended: llama-3.2-1b-instruct)")
        print("     - Start LMStudio server on localhost:1234")
        print("     - Install SDK: pip install lmstudio")
    
    if missing_env_vars:
        print("  3. Configure missing environment variables in .env file")
    
    print("\n🎯 NEXT STEPS:")
    print("  1. Start the server: uvicorn main:app --reload")
    print("  2. Visit http://localhost:8000/docs for API documentation")
    print("  3. Test with: curl -X POST http://localhost:8000/ocr/health")
    
    print("\n" + "="*60)

async def main():
    """Main startup function."""
    print("🚀 Starting Cambodian ID Card OCR API...")
    print("Performing system checks...\n")
    
    # Check system dependencies
    dependency_issues = check_system_dependencies()
    
    # Check environment configuration
    missing_env_vars = check_environment_config()
    
    # Check LMStudio connection
    lmstudio_available = await check_lmstudio_connection()
    
    # Initialize services
    services_initialized = await initialize_services()
    
    # Print summary
    print_startup_summary(
        dependency_issues,
        missing_env_vars,
        lmstudio_available,
        services_initialized
    )
    
    # Determine if we can start
    critical_issues = [issue for issue in dependency_issues if "tesseract" in issue.lower() or "pillow" in issue.lower()]
    
    if critical_issues:
        print("\n❌ CRITICAL ISSUES FOUND - Cannot start server")
        print("Please resolve the issues above before starting the server.")
        return False
    else:
        print("\n✅ SYSTEM READY - You can start the server")
        return True

if __name__ == "__main__":
    # Run startup checks
    success = asyncio.run(main())
    
    if success:
        print("\nTo start the server, run:")
        print("  uvicorn main:app --reload")
        sys.exit(0)
    else:
        sys.exit(1)

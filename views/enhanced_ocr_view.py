"""
Enhanced OCR API endpoints with LMStudio integration.

This module provides FastAPI routes for the enhanced OCR system that combines
traditional OCR with LMStudio AI for improved Cambodian ID card processing.
"""

import logging
from typing import Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Query
from fastapi.responses import JSONResponse

from controllers.enhanced_ocr_controller import (
    process_cambodian_id_enhanced,
    process_cambodian_id_with_comparison,
    get_enhanced_ocr_controller
)
from schemas.enhanced_ocr import (
    EnhancedCambodianIDCardResult,
    ProcessingRequest,
    OCRComparisonResult,
    CambodianIDCardOCRResult,
    ProcessingMethod
)
from services.lmstudio_service import get_lmstudio_service
from services.prompt_service import get_prompt_service

logger = logging.getLogger("EnhancedOCRView")

router = APIRouter(prefix="/ocr", tags=["enhanced-ocr"])

@router.get("/health")
async def health_check():
    """Health check endpoint for the enhanced OCR system."""
    lmstudio_service = get_lmstudio_service()
    
    return {
        "status": "healthy",
        "lmstudio_available": lmstudio_service.is_available,
        "services": {
            "tesseract": "available",
            "lmstudio": "available" if lmstudio_service.is_available else "unavailable",
            "image_processing": "available"
        }
    }

@router.post("/idcard/enhanced", response_model=EnhancedCambodianIDCardResult)
async def enhanced_ocr_idcard(
    file: UploadFile = File(...),
    use_lmstudio: bool = Query(True, description="Enable LMStudio AI enhancement"),
    enable_validation: bool = Query(True, description="Enable text validation"),
    enable_correction: bool = Query(True, description="Enable text correction"),
    lmstudio_model: Optional[str] = Query(None, description="Specific LMStudio model to use"),
    include_metadata: bool = Query(True, description="Include processing metadata"),
    fallback_to_traditional: bool = Query(True, description="Fallback to traditional OCR if LMStudio fails")
):
    """
    Enhanced OCR processing for Cambodian ID cards with LMStudio AI integration.
    
    This endpoint provides improved accuracy over traditional OCR by using
    LMStudio language models for text extraction and validation.
    """
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload an image.")
    
    # Create processing configuration
    config = ProcessingRequest(
        use_lmstudio=use_lmstudio,
        fallback_to_traditional=fallback_to_traditional,
        enable_validation=enable_validation,
        enable_correction=enable_correction,
        lmstudio_model=lmstudio_model,
        include_metadata=include_metadata
    )
    
    try:
        result = await process_cambodian_id_enhanced(file, config)
        
        # Log processing summary
        logger.info(
            f"Enhanced OCR completed - Method: {result.processing_metadata.processing_method}, "
            f"Confidence: {result.overall_confidence}, "
            f"Quality: {result.extraction_quality_score}"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Enhanced OCR processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Enhanced OCR processing failed: {str(e)}")

@router.post("/idcard/enhanced/compatible", response_model=CambodianIDCardOCRResult)
async def enhanced_ocr_idcard_compatible(
    file: UploadFile = File(...),
    use_lmstudio: bool = Query(True, description="Enable LMStudio AI enhancement")
):
    """
    Enhanced OCR with backward-compatible response format.
    
    This endpoint provides the same enhanced processing but returns results
    in the traditional format for backward compatibility.
    """
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload an image.")
    
    config = ProcessingRequest(use_lmstudio=use_lmstudio)
    
    try:
        enhanced_result = await process_cambodian_id_enhanced(file, config)
        
        # Convert to traditional format
        compatible_result = CambodianIDCardOCRResult.from_enhanced_result(enhanced_result)
        
        return compatible_result
        
    except Exception as e:
        logger.error(f"Enhanced OCR (compatible) processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Enhanced OCR processing failed: {str(e)}")

@router.post("/idcard/compare", response_model=OCRComparisonResult)
async def compare_ocr_methods(
    file: UploadFile = File(...),
    include_raw_data: bool = Query(True, description="Include raw OCR data in comparison")
):
    """
    Compare traditional OCR with enhanced LMStudio processing.
    
    This endpoint processes the same image with both methods and provides
    a detailed comparison of the results, including performance metrics.
    """
    if not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload an image.")
    
    config = ProcessingRequest(include_raw_data=include_raw_data, include_comparison=True)
    
    try:
        comparison_result = await process_cambodian_id_with_comparison(file, config)
        
        logger.info(
            f"OCR comparison completed - Recommended: {comparison_result.recommended_result}, "
            f"Improvements: {len(comparison_result.improvements_detected)}"
        )
        
        return comparison_result
        
    except Exception as e:
        logger.error(f"OCR comparison failed: {e}")
        raise HTTPException(status_code=500, detail=f"OCR comparison failed: {str(e)}")

@router.get("/models/available")
async def get_available_models():
    """Get list of available LMStudio models."""
    lmstudio_service = get_lmstudio_service()
    
    if not lmstudio_service.is_available:
        return {
            "available": False,
            "message": "LMStudio service not available",
            "models": []
        }
    
    try:
        # This would need to be implemented in the LMStudio service
        # For now, return basic info
        return {
            "available": True,
            "current_model": lmstudio_service.config.model_name or "default",
            "models": ["default"],  # Would be populated from actual LMStudio API
            "status": "connected"
        }
    except Exception as e:
        return {
            "available": False,
            "error": str(e),
            "models": []
        }

@router.get("/prompts/available")
async def get_available_prompts():
    """Get list of available prompt templates."""
    prompt_service = get_prompt_service()
    
    try:
        templates = prompt_service.list_available_templates()
        
        return {
            "available_templates": templates,
            "total_count": len(templates)
        }
    except Exception as e:
        logger.error(f"Failed to get prompt templates: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get prompt templates: {str(e)}")

@router.post("/initialize")
async def initialize_enhanced_ocr():
    """Initialize the enhanced OCR system and its dependencies."""
    try:
        controller = get_enhanced_ocr_controller()
        success = await controller.initialize()
        
        if success:
            return {
                "status": "initialized",
                "message": "Enhanced OCR system initialized successfully",
                "lmstudio_available": controller.lmstudio_service.is_available
            }
        else:
            return {
                "status": "partial",
                "message": "Enhanced OCR system partially initialized (LMStudio unavailable)",
                "lmstudio_available": False
            }
            
    except Exception as e:
        logger.error(f"Failed to initialize enhanced OCR: {e}")
        raise HTTPException(status_code=500, detail=f"Initialization failed: {str(e)}")

@router.get("/stats")
async def get_processing_stats():
    """Get processing statistics and system status."""
    lmstudio_service = get_lmstudio_service()
    
    return {
        "system_status": {
            "lmstudio_available": lmstudio_service.is_available,
            "lmstudio_config": {
                "host": lmstudio_service.config.host,
                "port": lmstudio_service.config.port,
                "model": lmstudio_service.config.model_name,
                "timeout": lmstudio_service.config.timeout
            } if lmstudio_service.is_available else None
        },
        "supported_features": {
            "traditional_ocr": True,
            "enhanced_ocr": lmstudio_service.is_available,
            "text_validation": True,
            "text_correction": lmstudio_service.is_available,
            "structured_extraction": lmstudio_service.is_available,
            "comparison_mode": True
        },
        "supported_languages": ["khm", "eng"],
        "supported_formats": ["image/jpeg", "image/png", "image/tiff", "image/bmp"]
    }

@router.post("/validate")
async def validate_extracted_text(
    khmer_text: str,
    english_text: str,
    use_lmstudio: bool = Query(True, description="Use LMStudio for validation")
):
    """
    Validate and correct extracted text using LMStudio.
    
    This endpoint can be used to validate text that was extracted
    from other sources or to get corrections for OCR output.
    """
    if not use_lmstudio:
        return {
            "validation_available": False,
            "message": "LMStudio validation disabled"
        }
    
    lmstudio_service = get_lmstudio_service()
    if not lmstudio_service.is_available:
        raise HTTPException(status_code=503, detail="LMStudio service not available")
    
    try:
        prompt_service = get_prompt_service()
        validation_prompt = prompt_service.get_validation_prompt()
        
        # Use LMStudio for validation
        validation_result = await lmstudio_service.enhance_ocr_text(
            khmer_text,
            english_text,
            validation_prompt
        )
        
        return {
            "validation_completed": validation_result.get("success", False),
            "validation_result": validation_result.get("enhanced_text"),
            "model_used": validation_result.get("model_used"),
            "original_khmer": khmer_text,
            "original_english": english_text
        }
        
    except Exception as e:
        logger.error(f"Text validation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Text validation failed: {str(e)}")

# Error handlers
@router.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions with detailed error information."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "status_code": exc.status_code,
            "endpoint": str(request.url),
            "method": request.method
        }
    )

"""
Enhanced OCR schemas for LMStudio-powered Cambodian ID card processing.

This module defines Pydantic models for the enhanced OCR system that combines
traditional OCR with LMStudio AI for improved accuracy and structured extraction.
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class ConfidenceLevel(str, Enum):
    """Confidence levels for OCR and AI extraction."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class ProcessingMethod(str, Enum):
    """Methods used for text processing."""
    TESSERACT_ONLY = "tesseract_only"
    LMSTUDIO_ENHANCED = "lmstudio_enhanced"
    HYBRID = "hybrid"

class ExtractionSource(str, Enum):
    """Source of extracted data."""
    KHMER_OCR = "khmer_ocr"
    ENGLISH_OCR = "english_ocr"
    LMSTUDIO_AI = "lmstudio_ai"
    REGEX_PARSING = "regex_parsing"
    HYBRID = "hybrid"

class FieldExtraction(BaseModel):
    """Detailed information about a single field extraction."""
    value: Optional[str] = None
    confidence: ConfidenceLevel = ConfidenceLevel.LOW
    source: ExtractionSource = ExtractionSource.REGEX_PARSING
    raw_text: Optional[str] = None
    corrections_made: Optional[List[str]] = None
    validation_notes: Optional[str] = None

class LMStudioProcessingResult(BaseModel):
    """Result from LMStudio AI processing."""
    success: bool = False
    model_used: Optional[str] = None
    processing_time_ms: Optional[int] = None
    enhanced_text: Optional[str] = None
    structured_data: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    error_message: Optional[str] = None

class OCRProcessingMetadata(BaseModel):
    """Metadata about the OCR processing pipeline."""
    tesseract_version: Optional[str] = None
    lmstudio_available: bool = False
    processing_method: ProcessingMethod = ProcessingMethod.TESSERACT_ONLY
    total_processing_time_ms: Optional[int] = None
    image_preprocessing_applied: List[str] = Field(default_factory=list)
    ocr_config_used: Optional[str] = None

class EnhancedCambodianIDCardResult(BaseModel):
    """Enhanced result for Cambodian ID card OCR with LMStudio integration."""
    
    # Basic extracted fields (enhanced versions)
    full_name: FieldExtraction = Field(default_factory=FieldExtraction)
    name_khmer: FieldExtraction = Field(default_factory=FieldExtraction)
    name_english: FieldExtraction = Field(default_factory=FieldExtraction)
    id_number: FieldExtraction = Field(default_factory=FieldExtraction)
    date_of_birth: FieldExtraction = Field(default_factory=FieldExtraction)
    gender: FieldExtraction = Field(default_factory=FieldExtraction)
    nationality: FieldExtraction = Field(default_factory=FieldExtraction)
    
    # Additional fields that might be extracted
    place_of_birth: FieldExtraction = Field(default_factory=FieldExtraction)
    address: FieldExtraction = Field(default_factory=FieldExtraction)
    height: FieldExtraction = Field(default_factory=FieldExtraction)
    
    # Raw OCR results
    raw_khmer: Optional[str] = None
    raw_english: Optional[str] = None
    
    # LMStudio AI processing results
    lmstudio_result: Optional[LMStudioProcessingResult] = None
    
    # Processing metadata
    processing_metadata: OCRProcessingMetadata = Field(default_factory=OCRProcessingMetadata)
    
    # Overall confidence and quality metrics
    overall_confidence: ConfidenceLevel = ConfidenceLevel.LOW
    extraction_quality_score: Optional[float] = None  # 0.0 to 1.0
    
    # Timestamp
    processed_at: datetime = Field(default_factory=datetime.utcnow)

class OCRComparisonResult(BaseModel):
    """Comparison between traditional OCR and LMStudio-enhanced results."""
    
    # Traditional OCR results (for comparison)
    traditional_result: Dict[str, Optional[str]] = Field(default_factory=dict)
    
    # Enhanced results
    enhanced_result: EnhancedCambodianIDCardResult
    
    # Comparison metrics
    improvements_detected: List[str] = Field(default_factory=list)
    confidence_improvements: Dict[str, float] = Field(default_factory=dict)
    
    # Performance metrics
    traditional_processing_time_ms: Optional[int] = None
    enhanced_processing_time_ms: Optional[int] = None
    
    # Recommendation
    recommended_result: str = "enhanced"  # "traditional" or "enhanced"
    recommendation_reason: Optional[str] = None

class BatchProcessingResult(BaseModel):
    """Result for batch processing of multiple ID cards."""
    
    total_processed: int = 0
    successful_extractions: int = 0
    failed_extractions: int = 0
    
    results: List[EnhancedCambodianIDCardResult] = Field(default_factory=list)
    
    # Aggregate statistics
    average_confidence: Optional[float] = None
    average_processing_time_ms: Optional[float] = None
    
    # Error summary
    error_summary: Dict[str, int] = Field(default_factory=dict)
    
    # Processing metadata
    batch_started_at: datetime = Field(default_factory=datetime.utcnow)
    batch_completed_at: Optional[datetime] = None

class ValidationResult(BaseModel):
    """Result of text validation and correction."""
    
    is_valid: bool = False
    confidence: ConfidenceLevel = ConfidenceLevel.LOW
    
    # Original and corrected text
    original_text: Optional[str] = None
    corrected_text: Optional[str] = None
    
    # Validation details
    validation_errors: List[str] = Field(default_factory=list)
    corrections_made: List[str] = Field(default_factory=list)
    
    # Field-specific validation
    field_validations: Dict[str, bool] = Field(default_factory=dict)
    
    # Suggestions for improvement
    suggestions: List[str] = Field(default_factory=list)

class ProcessingRequest(BaseModel):
    """Request configuration for enhanced OCR processing."""
    
    # Processing options
    use_lmstudio: bool = True
    fallback_to_traditional: bool = True
    enable_validation: bool = True
    enable_correction: bool = True
    
    # LMStudio configuration
    lmstudio_model: Optional[str] = None
    lmstudio_temperature: float = 0.1
    lmstudio_max_tokens: int = 1000
    
    # OCR configuration
    tesseract_config: Optional[str] = None
    image_preprocessing: List[str] = Field(default_factory=lambda: ["resize", "grayscale", "denoise", "threshold"])
    
    # Output options
    include_comparison: bool = False
    include_raw_data: bool = True
    include_metadata: bool = True

# Backward compatibility with existing schema
class CambodianIDCardOCRResult(BaseModel):
    """Traditional OCR result schema for backward compatibility."""
    
    full_name: Optional[str] = None
    name_kh: Optional[str] = None
    name_en: Optional[str] = None
    id_number: Optional[str] = None
    date_of_birth: Optional[str] = None
    gender: Optional[str] = None
    nationality: Optional[str] = "Cambodian"
    height: Optional[str] = None
    address: Optional[str] = None
    place_of_birth: Optional[str] = None
    raw_khmer: Optional[str] = None
    raw_english: Optional[str] = None
    
    @classmethod
    def from_enhanced_result(cls, enhanced: EnhancedCambodianIDCardResult) -> "CambodianIDCardOCRResult":
        """Convert enhanced result to traditional format."""
        return cls(
            full_name=enhanced.full_name.value,
            name_kh=enhanced.name_khmer.value,
            name_en=enhanced.name_english.value,
            id_number=enhanced.id_number.value,
            date_of_birth=enhanced.date_of_birth.value,
            gender=enhanced.gender.value,
            nationality=enhanced.nationality.value,
            height=enhanced.height.value,
            address=enhanced.address.value,
            place_of_birth=enhanced.place_of_birth.value,
            raw_khmer=enhanced.raw_khmer,
            raw_english=enhanced.raw_english
        )

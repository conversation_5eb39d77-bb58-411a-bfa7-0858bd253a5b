"""
LMStudio Service for AI-enhanced OCR processing.

This service provides integration with LMStudio for improved text extraction
and parsing of Cambodian ID cards using local language models.
"""

import os
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
import asyncio
from concurrent.futures import ThreadPoolExecutor

try:
    import lmstudio as lms
except ImportError:
    lms = None

logger = logging.getLogger("LMStudioService")

@dataclass
class LMStudioConfig:
    """Configuration for LMStudio connection and model settings."""
    host: str = "localhost"
    port: int = 1234
    model_name: Optional[str] = None
    timeout: int = 30
    max_tokens: int = 1000
    temperature: float = 0.1  # Low temperature for consistent extraction
    
    @classmethod
    def from_env(cls) -> "LMStudioConfig":
        """Create configuration from environment variables."""
        return cls(
            host=os.getenv("LMSTUDIO_HOST", "localhost"),
            port=int(os.getenv("LMSTUDIO_PORT", "1234")),
            model_name=os.getenv("LMSTUDIO_MODEL_NAME"),
            timeout=int(os.getenv("LMSTUDIO_TIMEOUT", "30")),
            max_tokens=int(os.getenv("LMSTUDIO_MAX_TOKENS", "1000")),
            temperature=float(os.getenv("LMSTUDIO_TEMPERATURE", "0.1"))
        )


class LMStudioService:
    """Service for interacting with LMStudio for AI-enhanced OCR."""
    
    def __init__(self, config: Optional[LMStudioConfig] = None):
        """Initialize LMStudio service with configuration."""
        self.config = config or LMStudioConfig.from_env()
        self._client = None
        self._model = None
        self._executor = ThreadPoolExecutor(max_workers=2)
        
        if lms is None:
            logger.warning("LMStudio SDK not available. Install with: pip install lmstudio")
            self._available = False
        else:
            self._available = True
            logger.info("LMStudio service initialized")
    
    @property
    def is_available(self) -> bool:
        """Check if LMStudio SDK is available."""
        return self._available
    
    async def initialize(self) -> bool:
        """Initialize connection to LMStudio."""
        if not self.is_available:
            logger.error("LMStudio SDK not available")
            return False
        
        try:
            # Run initialization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self._executor, 
                self._initialize_sync
            )
            return success
        except Exception as e:
            logger.error(f"Failed to initialize LMStudio connection: {e}")
            return False
    
    def _initialize_sync(self) -> bool:
        """Synchronous initialization of LMStudio connection."""
        try:
            # Initialize LMStudio client
            self._client = lms.Client()
            
            # Get available models or use specified model
            if self.config.model_name:
                self._model = lms.llm(self.config.model_name)
                logger.info(f"Using specified model: {self.config.model_name}")
            else:
                # Try to get the first available model
                self._model = lms.llm()
                logger.info("Using default LMStudio model")
            
            # Test connection with a simple prompt
            test_response = self._model.complete("Test", max_tokens=5)
            logger.info("LMStudio connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"LMStudio initialization failed: {e}")
            return False
    
    async def enhance_ocr_text(
        self, 
        raw_khmer: str, 
        raw_english: str, 
        prompt_template: str
    ) -> Dict[str, Any]:
        """
        Use LMStudio to enhance and structure OCR text extraction.
        
        Args:
            raw_khmer: Raw Khmer OCR text
            raw_english: Raw English OCR text
            prompt_template: Prompt template for text extraction
            
        Returns:
            Dictionary with enhanced extraction results
        """
        if not self.is_available or not self._model:
            logger.warning("LMStudio not available, returning empty result")
            return {"error": "LMStudio not available"}
        
        try:
            # Prepare the prompt with OCR data
            formatted_prompt = prompt_template.format(
                khmer_text=raw_khmer,
                english_text=raw_english
            )
            
            # Run LMStudio inference in thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                self._executor,
                self._generate_response,
                formatted_prompt
            )
            
            return {
                "enhanced_text": response,
                "model_used": self.config.model_name or "default",
                "success": True
            }
            
        except Exception as e:
            logger.error(f"LMStudio text enhancement failed: {e}")
            return {
                "error": str(e),
                "success": False
            }
    
    def _generate_response(self, prompt: str) -> str:
        """Generate response using LMStudio model (synchronous)."""
        try:
            response = self._model.complete(
                prompt,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            return response.strip()
        except Exception as e:
            logger.error(f"LMStudio generation failed: {e}")
            raise
    
    async def extract_structured_data(
        self, 
        raw_khmer: str, 
        raw_english: str,
        extraction_prompt: str
    ) -> Dict[str, Any]:
        """
        Extract structured data from OCR text using LMStudio.
        
        Args:
            raw_khmer: Raw Khmer OCR text
            raw_english: Raw English OCR text
            extraction_prompt: Prompt for structured data extraction
            
        Returns:
            Dictionary with extracted structured data
        """
        if not self.is_available or not self._model:
            return {"error": "LMStudio not available"}
        
        try:
            # Format prompt with OCR data
            formatted_prompt = extraction_prompt.format(
                khmer_text=raw_khmer,
                english_text=raw_english
            )
            
            # Generate structured response
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                self._executor,
                self._generate_response,
                formatted_prompt
            )
            
            # Try to parse JSON response
            import json
            try:
                structured_data = json.loads(response)
                return {
                    "data": structured_data,
                    "raw_response": response,
                    "success": True
                }
            except json.JSONDecodeError:
                # If not valid JSON, return as text
                return {
                    "text_response": response,
                    "success": True
                }
                
        except Exception as e:
            logger.error(f"Structured data extraction failed: {e}")
            return {
                "error": str(e),
                "success": False
            }
    
    async def cleanup(self):
        """Clean up resources."""
        if self._executor:
            self._executor.shutdown(wait=True)
        logger.info("LMStudio service cleaned up")


# Global service instance
_lmstudio_service: Optional[LMStudioService] = None

def get_lmstudio_service() -> LMStudioService:
    """Get or create the global LMStudio service instance."""
    global _lmstudio_service
    if _lmstudio_service is None:
        _lmstudio_service = LMStudioService()
    return _lmstudio_service

async def initialize_lmstudio_service() -> bool:
    """Initialize the global LMStudio service."""
    service = get_lmstudio_service()
    return await service.initialize()

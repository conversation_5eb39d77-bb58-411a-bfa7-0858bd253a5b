"""
Prompt Service for Cambodian ID Card Text Extraction.

This service provides specialized prompts and templates for extracting
structured information from Cambodian ID cards using LMStudio.
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger("PromptService")

@dataclass
class PromptTemplate:
    """Template for LMStudio prompts."""
    name: str
    template: str
    description: str
    expected_output: str

class CambodianIDPromptService:
    """Service for managing prompts specific to Cambodian ID card processing."""
    
    def __init__(self):
        """Initialize the prompt service with predefined templates."""
        self.templates = self._initialize_templates()
        logger.info("Cambodian ID Prompt Service initialized")
    
    def _initialize_templates(self) -> Dict[str, PromptTemplate]:
        """Initialize all prompt templates."""
        templates = {}
        
        # Text extraction and cleaning prompt
        templates["text_extraction"] = PromptTemplate(
            name="text_extraction",
            template="""You are an expert OCR text processor specializing in Cambodian ID cards. Your task is to clean and extract text from OCR output.

ROLE: Cambodian ID Card Text Extraction Specialist

INSTRUCTIONS:
1. Analyze the provided OCR text from a Cambodian ID card
2. Clean up OCR errors and noise
3. Extract only the actual text content, removing artifacts
4. Preserve both Khmer and English text accurately
5. Maintain the structure and relationships between text elements

INPUT OCR DATA:
Khmer Text: {khmer_text}
English Text: {english_text}

OUTPUT REQUIREMENTS:
- Return clean, readable text
- Preserve original language scripts (Khmer/English)
- Remove OCR artifacts like random characters, symbols, or noise
- Maintain spacing and structure where meaningful
- If text is unclear or corrupted, indicate with [UNCLEAR] tag

CLEANED TEXT:""",
            description="Cleans and extracts readable text from OCR output",
            expected_output="Clean, readable text with preserved language scripts"
        )
        
        # Structured data extraction prompt
        templates["structured_extraction"] = PromptTemplate(
            name="structured_extraction",
            template="""You are an expert at extracting structured information from Cambodian ID cards.

ROLE: Cambodian ID Card Data Extraction Specialist

TASK: Extract structured information from the provided OCR text and return it in JSON format.

CAMBODIAN ID CARD FIELDS TO EXTRACT:
- ឈ្មោះ (Name) / Name: Full name in Khmer and/or English
- លេខសម្គាល់ (ID Number) / ID Number: National ID number
- ថ្ងៃកំណើត (Date of Birth) / Date of Birth: Birth date
- ភេទ (Gender) / Sex: Gender (ប្រុស/Male, ស្រី/Female)
- សញ្ជាតិ (Nationality) / Nationality: Nationality
- ទីកន្លែងកំណើត (Place of Birth) / Place of Birth: Birth location
- អាសយដ្ឋាន (Address) / Address: Current address

INPUT OCR DATA:
Khmer Text: {khmer_text}
English Text: {english_text}

EXTRACTION RULES:
1. Look for both Khmer labels (ឈ្មោះ, លេខសម្គាល់, etc.) and English labels (Name, ID Number, etc.)
2. Extract the value that follows each label
3. For names, prioritize Khmer script if available
4. For ID numbers, extract only digits (remove spaces/formatting)
5. For dates, preserve the format found (DD/MM/YYYY, DD-MM-YYYY, etc.)
6. For gender, convert to standard format: "Male" or "Female"
7. If a field is not found or unclear, set it to null

OUTPUT FORMAT (JSON):
{{
    "name_khmer": "extracted Khmer name or null",
    "name_english": "extracted English name or null", 
    "id_number": "extracted ID number digits only or null",
    "date_of_birth": "extracted date or null",
    "gender": "Male/Female or null",
    "nationality": "extracted nationality or null",
    "place_of_birth": "extracted place of birth or null",
    "address": "extracted address or null",
    "confidence": "high/medium/low based on text clarity"
}}

JSON OUTPUT:""",
            description="Extracts structured data from Cambodian ID card text",
            expected_output="JSON object with extracted ID card fields"
        )
        
        # Text validation and correction prompt
        templates["text_validation"] = PromptTemplate(
            name="text_validation",
            template="""You are a Khmer language expert specializing in correcting OCR errors in Cambodian text.

ROLE: Khmer Text Validation and Correction Specialist

TASK: Validate and correct the extracted text from a Cambodian ID card, focusing on common OCR errors.

COMMON KHMER OCR ERRORS TO FIX:
- Confused characters: ក/គ, ត/ដ, ន/រ, ស/ច, etc.
- Missing or extra diacritics (vowels and consonant clusters)
- Incorrect word boundaries
- Mixed up similar-looking characters

VALIDATION RULES:
1. Check if Khmer text follows proper Khmer spelling rules
2. Validate that names are realistic Cambodian names
3. Ensure ID numbers are 9-12 digits
4. Verify date formats are logical
5. Check that addresses contain real Cambodian place names

INPUT TEXT TO VALIDATE:
Khmer Text: {khmer_text}
English Text: {english_text}

PROVIDE:
1. Corrected Khmer text (if errors found)
2. Validation status for each field
3. Confidence level in the corrections
4. List of corrections made

OUTPUT FORMAT:
CORRECTED_KHMER: [corrected text or "NO_CHANGES_NEEDED"]
VALIDATION_STATUS: [field_name: valid/invalid/uncertain]
CONFIDENCE: [high/medium/low]
CORRECTIONS_MADE: [list of specific corrections]""",
            description="Validates and corrects Khmer text OCR errors",
            expected_output="Corrected text with validation status"
        )
        
        # Field-specific extraction prompt
        templates["field_specific"] = PromptTemplate(
            name="field_specific",
            template="""You are an expert at extracting specific fields from Cambodian ID cards.

ROLE: Cambodian ID Card Field Extraction Specialist

TASK: Extract the specific field "{field_name}" from the provided OCR text.

FIELD EXTRACTION RULES FOR {field_name}:
{field_rules}

INPUT OCR DATA:
Khmer Text: {khmer_text}
English Text: {english_text}

EXTRACTION PROCESS:
1. Look for field labels in both Khmer and English
2. Identify the value associated with the field
3. Clean and format the value appropriately
4. Provide confidence level

OUTPUT:
EXTRACTED_VALUE: [the extracted value or null]
CONFIDENCE: [high/medium/low]
SOURCE: [khmer/english/both]
NOTES: [any relevant observations]""",
            description="Extracts a specific field from ID card text",
            expected_output="Specific field value with metadata"
        )
        
        return templates
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific prompt template by name."""
        return self.templates.get(template_name)
    
    def get_text_extraction_prompt(self) -> str:
        """Get the text extraction prompt template."""
        template = self.get_template("text_extraction")
        return template.template if template else ""
    
    def get_structured_extraction_prompt(self) -> str:
        """Get the structured data extraction prompt template."""
        template = self.get_template("structured_extraction")
        return template.template if template else ""
    
    def get_validation_prompt(self) -> str:
        """Get the text validation prompt template."""
        template = self.get_template("text_validation")
        return template.template if template else ""
    
    def get_field_specific_prompt(self, field_name: str) -> str:
        """Get a field-specific extraction prompt."""
        template = self.get_template("field_specific")
        if not template:
            return ""
        
        # Define field-specific rules
        field_rules = self._get_field_rules(field_name)
        
        return template.template.format(
            field_name=field_name,
            field_rules=field_rules
        )
    
    def _get_field_rules(self, field_name: str) -> str:
        """Get extraction rules for a specific field."""
        rules = {
            "name": """
- Look for ឈ្មោះ (Khmer) or "Name" (English) labels
- Extract full name, preserving Khmer script if available
- Names typically contain 2-4 words
- Remove any title prefixes (Mr., Mrs., etc.)
            """,
            "id_number": """
- Look for លេខសម្គាល់ (Khmer) or "ID Number" (English) labels
- Extract only digits, remove spaces and formatting
- Should be 9-12 digits long
- Validate that it's a reasonable ID number format
            """,
            "date_of_birth": """
- Look for ថ្ងៃកំណើត (Khmer) or "Date of Birth"/"DOB" (English) labels
- Accept formats: DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY
- Validate that the date is reasonable (not future, not too old)
- Convert to consistent format if needed
            """,
            "gender": """
- Look for ភេទ (Khmer) or "Sex"/"Gender" (English) labels
- Extract ប្រុស (Male) or ស្រី (Female) in Khmer
- Or "M"/"Male" or "F"/"Female" in English
- Standardize to "Male" or "Female"
            """,
            "nationality": """
- Look for សញ្ជាតិ (Khmer) or "Nationality" (English) labels
- Most commonly "ខ្មែរ" (Khmer) or "Cambodian" (English)
- Extract the nationality value following the label
            """
        }
        
        return rules.get(field_name.lower(), "No specific rules defined for this field.")
    
    def list_available_templates(self) -> Dict[str, str]:
        """List all available prompt templates with descriptions."""
        return {
            name: template.description 
            for name, template in self.templates.items()
        }


# Global service instance
_prompt_service: Optional[CambodianIDPromptService] = None

def get_prompt_service() -> CambodianIDPromptService:
    """Get or create the global prompt service instance."""
    global _prompt_service
    if _prompt_service is None:
        _prompt_service = CambodianIDPromptService()
    return _prompt_service

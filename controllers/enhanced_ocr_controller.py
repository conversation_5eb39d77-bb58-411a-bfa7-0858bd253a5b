"""
Enhanced OCR Controller with LMStudio Integration.

This controller combines traditional Tesseract OCR with LMStudio AI
for improved accuracy in Cambodian ID card text extraction.
"""

import time
import json
import logging
from typing import Optional, Dict, Any, List
from fastapi import UploadFile, HTTPException
from PIL import Image
import io

# Import existing OCR functionality
from controllers.ocr_controller import preprocess_image, parse_cambodian_id_ocr
import pytesseract

# Import new services and schemas
from services.lmstudio_service import get_lmstudio_service
from services.prompt_service import get_prompt_service
from schemas.enhanced_ocr import (
    EnhancedCambodianIDCardResult,
    FieldExtraction,
    LMStudioProcessingResult,
    OCRProcessingMetadata,
    ProcessingRequest,
    ConfidenceLevel,
    ProcessingMethod,
    ExtractionSource,
    OCRComparisonResult,
    CambodianIDCardOCRResult
)

logger = logging.getLogger("EnhancedOCR")

class EnhancedOCRController:
    """Enhanced OCR controller with LMStudio integration."""
    
    def __init__(self):
        """Initialize the enhanced OCR controller."""
        self.lmstudio_service = get_lmstudio_service()
        self.prompt_service = get_prompt_service()
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the controller and its services."""
        if self._initialized:
            return True
        
        try:
            # Initialize LMStudio service
            lmstudio_available = await self.lmstudio_service.initialize()
            
            self._initialized = True
            logger.info(f"Enhanced OCR Controller initialized (LMStudio: {'available' if lmstudio_available else 'unavailable'})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced OCR Controller: {e}")
            return False
    
    async def process_cambodian_id_enhanced(
        self, 
        file: UploadFile,
        request_config: Optional[ProcessingRequest] = None
    ) -> EnhancedCambodianIDCardResult:
        """
        Process Cambodian ID card with enhanced OCR and LMStudio integration.
        
        Args:
            file: Uploaded image file
            request_config: Processing configuration options
            
        Returns:
            Enhanced OCR result with AI improvements
        """
        start_time = time.time()
        
        # Ensure controller is initialized
        if not self._initialized:
            await self.initialize()
        
        # Use default config if none provided
        if request_config is None:
            request_config = ProcessingRequest()
        
        try:
            # Validate file type
            if not file.content_type.startswith("image/"):
                raise HTTPException(status_code=400, detail="Invalid file type. Only images are allowed.")
            
            # Read and preprocess image
            image_bytes = await file.read()
            image = Image.open(io.BytesIO(image_bytes)).convert("RGB")
            processed_image = preprocess_image(image)
            
            # Perform traditional OCR
            ocr_start = time.time()
            khmer_text = pytesseract.image_to_string(processed_image, lang='khm', config='--oem 1 --psm 11')
            english_text = pytesseract.image_to_string(processed_image, lang='eng', config='--oem 1 --psm 11')
            ocr_time = int((time.time() - ocr_start) * 1000)
            
            # Parse with traditional method for baseline
            traditional_data = parse_cambodian_id_ocr(khmer_text, english_text)
            
            # Initialize result object
            result = EnhancedCambodianIDCardResult()
            result.raw_khmer = khmer_text
            result.raw_english = english_text
            
            # Set up processing metadata
            result.processing_metadata = OCRProcessingMetadata(
                lmstudio_available=self.lmstudio_service.is_available,
                processing_method=ProcessingMethod.TESSERACT_ONLY,
                image_preprocessing_applied=["resize", "grayscale", "denoise", "threshold"],
                ocr_config_used="--oem 1 --psm 11"
            )
            
            # Try LMStudio enhancement if available and requested
            lmstudio_result = None
            if request_config.use_lmstudio and self.lmstudio_service.is_available:
                lmstudio_result = await self._enhance_with_lmstudio(
                    khmer_text, 
                    english_text, 
                    request_config
                )
                result.lmstudio_result = lmstudio_result
                
                if lmstudio_result.success:
                    result.processing_metadata.processing_method = ProcessingMethod.LMSTUDIO_ENHANCED
            
            # Extract and populate fields
            await self._populate_enhanced_fields(
                result, 
                traditional_data, 
                lmstudio_result, 
                request_config
            )
            
            # Calculate overall metrics
            total_time = int((time.time() - start_time) * 1000)
            result.processing_metadata.total_processing_time_ms = total_time
            result.overall_confidence = self._calculate_overall_confidence(result)
            result.extraction_quality_score = self._calculate_quality_score(result)
            
            logger.info(f"Enhanced OCR processing completed in {total_time}ms")
            return result
            
        except Exception as e:
            logger.error(f"Enhanced OCR processing failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Enhanced OCR processing failed: {str(e)}")
    
    async def _enhance_with_lmstudio(
        self, 
        khmer_text: str, 
        english_text: str,
        config: ProcessingRequest
    ) -> LMStudioProcessingResult:
        """Enhance OCR results using LMStudio."""
        start_time = time.time()
        
        try:
            # Get structured extraction prompt
            extraction_prompt = self.prompt_service.get_structured_extraction_prompt()
            
            # Use LMStudio for structured extraction
            enhancement_result = await self.lmstudio_service.extract_structured_data(
                khmer_text,
                english_text,
                extraction_prompt
            )
            
            processing_time = int((time.time() - start_time) * 1000)
            
            if enhancement_result.get("success"):
                return LMStudioProcessingResult(
                    success=True,
                    model_used=self.lmstudio_service.config.model_name,
                    processing_time_ms=processing_time,
                    structured_data=enhancement_result.get("data"),
                    enhanced_text=enhancement_result.get("text_response"),
                    confidence_score=0.8  # Default confidence for successful processing
                )
            else:
                return LMStudioProcessingResult(
                    success=False,
                    processing_time_ms=processing_time,
                    error_message=enhancement_result.get("error", "Unknown error")
                )
                
        except Exception as e:
            processing_time = int((time.time() - start_time) * 1000)
            logger.error(f"LMStudio enhancement failed: {e}")
            return LMStudioProcessingResult(
                success=False,
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    
    async def _populate_enhanced_fields(
        self,
        result: EnhancedCambodianIDCardResult,
        traditional_data: Dict[str, Optional[str]],
        lmstudio_result: Optional[LMStudioProcessingResult],
        config: ProcessingRequest
    ):
        """Populate enhanced fields from traditional and LMStudio data."""
        
        # Helper function to create field extraction
        def create_field_extraction(
            value: Optional[str],
            source: ExtractionSource,
            confidence: ConfidenceLevel,
            raw_text: Optional[str] = None
        ) -> FieldExtraction:
            return FieldExtraction(
                value=value,
                confidence=confidence,
                source=source,
                raw_text=raw_text
            )
        
        # Start with traditional OCR data
        result.full_name = create_field_extraction(
            traditional_data.get("name"),
            ExtractionSource.REGEX_PARSING,
            ConfidenceLevel.MEDIUM if traditional_data.get("name") else ConfidenceLevel.LOW
        )
        
        result.name_khmer = create_field_extraction(
            traditional_data.get("name_kh"),
            ExtractionSource.KHMER_OCR,
            ConfidenceLevel.MEDIUM if traditional_data.get("name_kh") else ConfidenceLevel.LOW
        )
        
        result.name_english = create_field_extraction(
            traditional_data.get("name_en"),
            ExtractionSource.ENGLISH_OCR,
            ConfidenceLevel.MEDIUM if traditional_data.get("name_en") else ConfidenceLevel.LOW
        )
        
        result.id_number = create_field_extraction(
            traditional_data.get("id_number"),
            ExtractionSource.REGEX_PARSING,
            ConfidenceLevel.HIGH if traditional_data.get("id_number") and len(traditional_data.get("id_number", "")) >= 9 else ConfidenceLevel.LOW
        )
        
        result.date_of_birth = create_field_extraction(
            traditional_data.get("dob"),
            ExtractionSource.REGEX_PARSING,
            ConfidenceLevel.MEDIUM if traditional_data.get("dob") else ConfidenceLevel.LOW
        )
        
        result.gender = create_field_extraction(
            traditional_data.get("gender"),
            ExtractionSource.REGEX_PARSING,
            ConfidenceLevel.HIGH if traditional_data.get("gender") in ["Male", "Female"] else ConfidenceLevel.LOW
        )
        
        result.nationality = create_field_extraction(
            traditional_data.get("nationality", "Cambodian"),
            ExtractionSource.REGEX_PARSING,
            ConfidenceLevel.HIGH
        )
        
        # Enhance with LMStudio data if available
        if lmstudio_result and lmstudio_result.success and lmstudio_result.structured_data:
            await self._merge_lmstudio_data(result, lmstudio_result.structured_data)
    
    async def _merge_lmstudio_data(
        self,
        result: EnhancedCambodianIDCardResult,
        lmstudio_data: Dict[str, Any]
    ):
        """Merge LMStudio extracted data with existing results."""
        
        # Helper to update field if LMStudio provides better data
        def update_if_better(field: FieldExtraction, lm_value: Any, field_name: str):
            if lm_value and lm_value != "null" and str(lm_value).strip():
                # LMStudio provided a value, check if it's better
                if not field.value or field.confidence == ConfidenceLevel.LOW:
                    field.value = str(lm_value).strip()
                    field.source = ExtractionSource.LMSTUDIO_AI
                    field.confidence = ConfidenceLevel.HIGH
                    logger.info(f"LMStudio improved {field_name}: {field.value}")
        
        # Update fields with LMStudio data
        update_if_better(result.name_khmer, lmstudio_data.get("name_khmer"), "name_khmer")
        update_if_better(result.name_english, lmstudio_data.get("name_english"), "name_english")
        update_if_better(result.id_number, lmstudio_data.get("id_number"), "id_number")
        update_if_better(result.date_of_birth, lmstudio_data.get("date_of_birth"), "date_of_birth")
        update_if_better(result.gender, lmstudio_data.get("gender"), "gender")
        update_if_better(result.nationality, lmstudio_data.get("nationality"), "nationality")
        update_if_better(result.place_of_birth, lmstudio_data.get("place_of_birth"), "place_of_birth")
        update_if_better(result.address, lmstudio_data.get("address"), "address")
        
        # Update full_name based on best available name
        if result.name_khmer.value:
            result.full_name.value = result.name_khmer.value
            result.full_name.source = ExtractionSource.LMSTUDIO_AI
            result.full_name.confidence = result.name_khmer.confidence
        elif result.name_english.value:
            result.full_name.value = result.name_english.value
            result.full_name.source = ExtractionSource.LMSTUDIO_AI
            result.full_name.confidence = result.name_english.confidence
    
    def _calculate_overall_confidence(self, result: EnhancedCambodianIDCardResult) -> ConfidenceLevel:
        """Calculate overall confidence based on individual field confidences."""
        fields = [
            result.full_name,
            result.id_number,
            result.date_of_birth,
            result.gender,
            result.nationality
        ]
        
        confidence_scores = []
        for field in fields:
            if field.value:
                if field.confidence == ConfidenceLevel.HIGH:
                    confidence_scores.append(3)
                elif field.confidence == ConfidenceLevel.MEDIUM:
                    confidence_scores.append(2)
                else:
                    confidence_scores.append(1)
        
        if not confidence_scores:
            return ConfidenceLevel.LOW
        
        avg_score = sum(confidence_scores) / len(confidence_scores)
        
        if avg_score >= 2.5:
            return ConfidenceLevel.HIGH
        elif avg_score >= 1.5:
            return ConfidenceLevel.MEDIUM
        else:
            return ConfidenceLevel.LOW
    
    def _calculate_quality_score(self, result: EnhancedCambodianIDCardResult) -> float:
        """Calculate extraction quality score (0.0 to 1.0)."""
        total_fields = 6  # name, id, dob, gender, nationality, plus bonus for extras
        extracted_fields = 0
        confidence_bonus = 0
        
        fields = [
            result.full_name,
            result.id_number,
            result.date_of_birth,
            result.gender,
            result.nationality
        ]
        
        for field in fields:
            if field.value:
                extracted_fields += 1
                if field.confidence == ConfidenceLevel.HIGH:
                    confidence_bonus += 0.2
                elif field.confidence == ConfidenceLevel.MEDIUM:
                    confidence_bonus += 0.1
        
        # Bonus for additional fields
        if result.place_of_birth.value:
            extracted_fields += 0.5
        if result.address.value:
            extracted_fields += 0.5
        
        base_score = extracted_fields / total_fields
        final_score = min(1.0, base_score + (confidence_bonus / total_fields))
        
        return round(final_score, 2)


# Global controller instance
_enhanced_controller: Optional[EnhancedOCRController] = None

def get_enhanced_ocr_controller() -> EnhancedOCRController:
    """Get or create the global enhanced OCR controller instance."""
    global _enhanced_controller
    if _enhanced_controller is None:
        _enhanced_controller = EnhancedOCRController()
    return _enhanced_controller

async def process_cambodian_id_enhanced(
    file: UploadFile,
    config: Optional[ProcessingRequest] = None
) -> EnhancedCambodianIDCardResult:
    """Process Cambodian ID card with enhanced OCR (convenience function)."""
    controller = get_enhanced_ocr_controller()
    return await controller.process_cambodian_id_enhanced(file, config)

async def process_cambodian_id_with_comparison(
    file: UploadFile,
    config: Optional[ProcessingRequest] = None
) -> OCRComparisonResult:
    """Process with both traditional and enhanced methods for comparison."""
    # Import here to avoid circular imports
    from controllers.ocr_controller import process_cambodian_id_ocr
    
    start_time = time.time()
    
    # Process with traditional method
    traditional_start = time.time()
    traditional_result = await process_cambodian_id_ocr(file)
    traditional_time = int((time.time() - traditional_start) * 1000)
    
    # Reset file position for enhanced processing
    await file.seek(0)
    
    # Process with enhanced method
    enhanced_start = time.time()
    enhanced_result = await process_cambodian_id_enhanced(file, config)
    enhanced_time = int((time.time() - enhanced_start) * 1000)
    
    # Create comparison result
    comparison = OCRComparisonResult(
        traditional_result={
            "full_name": traditional_result.full_name,
            "id_number": traditional_result.id_number,
            "date_of_birth": traditional_result.date_of_birth,
            "gender": traditional_result.gender,
            "nationality": traditional_result.nationality
        },
        enhanced_result=enhanced_result,
        traditional_processing_time_ms=traditional_time,
        enhanced_processing_time_ms=enhanced_time
    )
    
    # Analyze improvements
    improvements = []
    if enhanced_result.overall_confidence.value != "low":
        improvements.append("Overall confidence improved")
    
    if enhanced_result.lmstudio_result and enhanced_result.lmstudio_result.success:
        improvements.append("AI enhancement successful")
    
    comparison.improvements_detected = improvements
    comparison.recommended_result = "enhanced" if improvements else "traditional"
    
    return comparison

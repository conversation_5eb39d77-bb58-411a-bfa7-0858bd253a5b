from fastapi import FastAPI
from views.user_view import router as user_router
from views.ocr_view import router as ocr_router
from views.enhanced_ocr_view import router as enhanced_ocr_router

app = FastAPI(
    title="Cambodian ID Card OCR API",
    description="API for processing Cambodian ID cards with traditional and AI-enhanced OCR",
    version="2.0.0"
)

app.include_router(user_router)
app.include_router(ocr_router)
app.include_router(enhanced_ocr_router)

# Optionally, add root endpoint
def root():
    return {"message": "Welcome to the FastAPI MVC project!"}

app.get("/")(root)
